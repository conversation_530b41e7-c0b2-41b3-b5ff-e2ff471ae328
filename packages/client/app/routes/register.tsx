import type { Route } from "./+types/register";
import { Link } from "react-router";
import { AuthLayout } from "../components/auth/AuthLayout";
import { SocialLoginButton } from "../components/auth/SocialLoginButton";
import { EmailInput } from "../components/auth/EmailInput";

export function meta({}: Route.MetaArgs) {
  return [
    { title: "Create Account - Vast" },
    { name: "description", content: "Create your Vast account" },
  ];
}

export default function Register() {
  const handleSocialLogin = (provider: 'github' | 'google') => {
    // Mock implementation - in real app, this would redirect to OAuth provider
    console.log(`Initiating ${provider} registration...`);
    alert(`${provider} registration would be initiated here. This is a mock implementation.`);
  };

  const handleEmailRegister = (email: string) => {
    // Mock implementation - in real app, this would send a magic link for registration
    console.log(`Sending registration link to ${email}...`);
    alert(`Registration link would be sent to ${email}. This is a mock implementation.`);
  };

  return (
    <AuthLayout 
      title="Create your account" 
      subtitle="Get started with Vast today"
    >
      <div style={styles.socialButtons}>
        <SocialLoginButton 
          provider="github" 
          onClick={() => handleSocialLogin('github')} 
        />
        <SocialLoginButton 
          provider="google" 
          onClick={() => handleSocialLogin('google')} 
        />
      </div>

      <div style={styles.divider}>
        <span style={styles.dividerLine}></span>
        <span style={styles.dividerText}>or</span>
        <span style={styles.dividerLine}></span>
      </div>

      <EmailInput 
        onSubmit={handleEmailRegister}
        placeholder="Enter your email address"
        buttonText="Create account"
      />

      <div style={styles.terms}>
        <p style={styles.termsText}>
          By creating an account, you agree to our{' '}
          <a href="#" style={styles.link}>Terms of Service</a>
          {' '}and{' '}
          <a href="#" style={styles.link}>Privacy Policy</a>
        </p>
      </div>

      <div style={styles.footer}>
        <p style={styles.footerText}>
          Already have an account?{' '}
          <Link to="/login" style={styles.link}>
            Sign in
          </Link>
        </p>
      </div>
    </AuthLayout>
  );
}

const styles = {
  socialButtons: {
    display: 'flex',
    flexDirection: 'column' as const,
    gap: '0.75rem',
  },
  divider: {
    display: 'flex',
    alignItems: 'center',
    margin: '1.5rem 0',
  },
  dividerLine: {
    flex: 1,
    height: '1px',
    backgroundColor: '#e5e7eb',
  },
  dividerText: {
    padding: '0 1rem',
    fontSize: '0.875rem',
    color: '#6b7280',
  },
  terms: {
    marginTop: '1rem',
  },
  termsText: {
    fontSize: '0.75rem',
    color: '#6b7280',
    margin: 0,
    lineHeight: '1.4',
    textAlign: 'center' as const,
  },
  footer: {
    textAlign: 'center' as const,
    marginTop: '1.5rem',
  },
  footerText: {
    fontSize: '0.875rem',
    color: '#6b7280',
    margin: 0,
  },
  link: {
    color: '#3b82f6',
    textDecoration: 'none',
    fontWeight: '500',
  },
};
