import type { Route } from "./+types/login";
import { Link } from "react-router";
import { AuthLayout } from "../components/auth/AuthLayout";
import { SocialLoginButton } from "../components/auth/SocialLoginButton";
import { EmailInput } from "../components/auth/EmailInput";

export function meta({}: Route.MetaArgs) {
  return [
    { title: "Sign In - Vast" },
    { name: "description", content: "Sign in to your Vast account" },
  ];
}

export default function Login() {
  const handleSocialLogin = (provider: 'github' | 'google') => {
    // Mock implementation - in real app, this would redirect to OAuth provider
    console.log(`Initiating ${provider} login...`);
    alert(`${provider} login would be initiated here. This is a mock implementation.`);
  };

  const handleEmailLogin = (email: string) => {
    // Mock implementation - in real app, this would send a magic link
    console.log(`Sending magic link to ${email}...`);
    alert(`Magic link would be sent to ${email}. This is a mock implementation.`);
  };

  return (
    <AuthLayout 
      title="Welcome back" 
      subtitle="Sign in to your account"
    >
      <div style={styles.socialButtons}>
        <SocialLoginButton 
          provider="github" 
          onClick={() => handleSocialLogin('github')} 
        />
        <SocialLoginButton 
          provider="google" 
          onClick={() => handleSocialLogin('google')} 
        />
      </div>

      <div style={styles.divider}>
        <span style={styles.dividerLine}></span>
        <span style={styles.dividerText}>or</span>
        <span style={styles.dividerLine}></span>
      </div>

      <EmailInput 
        onSubmit={handleEmailLogin}
        placeholder="Enter your email address"
        buttonText="Send magic link"
      />

      <div style={styles.footer}>
        <p style={styles.footerText}>
          Don't have an account?{' '}
          <Link to="/register" style={styles.link}>
            Create one
          </Link>
        </p>
      </div>
    </AuthLayout>
  );
}

const styles = {
  socialButtons: {
    display: 'flex',
    flexDirection: 'column' as const,
    gap: '0.75rem',
  },
  divider: {
    display: 'flex',
    alignItems: 'center',
    margin: '1.5rem 0',
  },
  dividerLine: {
    flex: 1,
    height: '1px',
    backgroundColor: '#e5e7eb',
  },
  dividerText: {
    padding: '0 1rem',
    fontSize: '0.875rem',
    color: '#6b7280',
  },
  footer: {
    textAlign: 'center' as const,
    marginTop: '1.5rem',
  },
  footerText: {
    fontSize: '0.875rem',
    color: '#6b7280',
    margin: 0,
  },
  link: {
    color: '#3b82f6',
    textDecoration: 'none',
    fontWeight: '500',
  },
};
