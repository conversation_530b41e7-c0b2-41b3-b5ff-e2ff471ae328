import type { Route } from "./+types/landing";
import { Link } from "react-router";

export function meta({}: Route.MetaArgs) {
  return [
    { title: "Welcome to Vast" },
    {
      name: "description",
      content: "Welcome to Vast - Your modern application",
    },
  ];
}

export default function Landing() {
  return (
    <div style={styles.container}>
      <div style={styles.content}>
        <header style={styles.header}>
          <h1 style={styles.title}>Welcome to Vast</h1>
          <p style={styles.subtitle}>
            A modern application built with React Router and FluentUI
          </p>
        </header>

        <div style={styles.actions}>
          <Link to="/login" style={styles.primaryButton}>
            Sign In
          </Link>
          <Link to="/register" style={styles.secondaryButton}>
            Create Account
          </Link>
        </div>

        <div style={styles.features}>
          <div style={styles.feature}>
            <h3 style={styles.featureTitle}>🔐 Secure Authentication</h3>
            <p style={styles.featureDescription}>
              Passwordless authentication with GitHub and Google social login
            </p>
          </div>
          <div style={styles.feature}>
            <h3 style={styles.featureTitle}>⚡ Modern Stack</h3>
            <p style={styles.featureDescription}>
              Built with React Router 7 and FluentUI components
            </p>
          </div>
          <div style={styles.feature}>
            <h3 style={styles.featureTitle}>🎨 Beautiful Design</h3>
            <p style={styles.featureDescription}>
              Clean, responsive design that works on all devices
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

const styles = {
  container: {
    minHeight: "100vh",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    padding: "2rem",
    background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
  },
  content: {
    maxWidth: "800px",
    textAlign: "center" as const,
    color: "#ffffff",
  },
  header: {
    marginBottom: "3rem",
  },
  title: {
    fontSize: "3rem",
    fontWeight: "bold",
    margin: "0 0 1rem 0",
    textShadow: "0 2px 4px rgba(0, 0, 0, 0.3)",
  },
  subtitle: {
    fontSize: "1.25rem",
    margin: 0,
    opacity: 0.9,
  },
  actions: {
    display: "flex",
    gap: "1rem",
    justifyContent: "center",
    marginBottom: "4rem",
    flexWrap: "wrap" as const,
  },
  primaryButton: {
    display: "inline-block",
    padding: "1rem 2rem",
    backgroundColor: "#ffffff",
    color: "#667eea",
    textDecoration: "none",
    borderRadius: "8px",
    fontWeight: "600",
    fontSize: "1rem",
    transition: "transform 0.2s ease, box-shadow 0.2s ease",
    boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
  },
  secondaryButton: {
    display: "inline-block",
    padding: "1rem 2rem",
    backgroundColor: "transparent",
    color: "#ffffff",
    textDecoration: "none",
    borderRadius: "8px",
    fontWeight: "600",
    fontSize: "1rem",
    border: "2px solid #ffffff",
    transition: "background-color 0.2s ease",
  },
  features: {
    display: "grid",
    gridTemplateColumns: "repeat(auto-fit, minmax(250px, 1fr))",
    gap: "2rem",
    marginTop: "2rem",
  },
  feature: {
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    padding: "2rem",
    borderRadius: "12px",
    backdropFilter: "blur(10px)",
  },
  featureTitle: {
    fontSize: "1.25rem",
    fontWeight: "600",
    margin: "0 0 1rem 0",
  },
  featureDescription: {
    fontSize: "0.875rem",
    margin: 0,
    opacity: 0.9,
    lineHeight: "1.5",
  },
};
