import type { Route } from "./+types/auth-callback";
import { useEffect, useState } from "react";
import { useNavigate, useSearchParams } from "react-router";

export function meta({}: Route.MetaArgs) {
  return [
    { title: "Authenticating - Vast" },
    { name: "description", content: "Processing your authentication..." },
  ];
}

export default function AuthCallback() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('Processing your authentication...');

  useEffect(() => {
    // Mock authentication processing
    const provider = searchParams.get('provider');
    const code = searchParams.get('code');
    const error = searchParams.get('error');

    if (error) {
      setStatus('error');
      setMessage(`Authentication failed: ${error}`);
      setTimeout(() => {
        navigate('/login');
      }, 3000);
      return;
    }

    if (provider && code) {
      // Mock successful authentication
      setTimeout(() => {
        setStatus('success');
        setMessage('Authentication successful! Redirecting...');
        
        // In a real app, you would:
        // 1. Exchange the code for tokens
        // 2. Store the tokens securely
        // 3. Redirect to the dashboard or intended page
        setTimeout(() => {
          navigate('/'); // Redirect to dashboard or home
        }, 2000);
      }, 2000);
    } else {
      setStatus('error');
      setMessage('Invalid authentication parameters');
      setTimeout(() => {
        navigate('/login');
      }, 3000);
    }
  }, [searchParams, navigate]);

  return (
    <div style={styles.container}>
      <div style={styles.card}>
        <div style={styles.content}>
          {status === 'loading' && (
            <div style={styles.spinner}>
              <div style={styles.spinnerCircle}></div>
            </div>
          )}
          
          {status === 'success' && (
            <div style={styles.successIcon}>✓</div>
          )}
          
          {status === 'error' && (
            <div style={styles.errorIcon}>✗</div>
          )}
          
          <h2 style={{
            ...styles.title,
            color: status === 'error' ? '#ef4444' : status === 'success' ? '#10b981' : '#6b7280'
          }}>
            {status === 'loading' && 'Authenticating...'}
            {status === 'success' && 'Success!'}
            {status === 'error' && 'Authentication Failed'}
          </h2>
          
          <p style={styles.message}>{message}</p>
          
          {status === 'error' && (
            <button 
              onClick={() => navigate('/login')}
              style={styles.button}
            >
              Back to Login
            </button>
          )}
        </div>
      </div>
    </div>
  );
}

const styles = {
  container: {
    minHeight: '100vh',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    padding: '1rem',
    backgroundColor: '#f8fafc',
  },
  card: {
    width: '100%',
    maxWidth: '400px',
    backgroundColor: '#ffffff',
    borderRadius: '8px',
    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    padding: '3rem 2rem',
  },
  content: {
    textAlign: 'center' as const,
    display: 'flex',
    flexDirection: 'column' as const,
    alignItems: 'center',
    gap: '1.5rem',
  },
  spinner: {
    width: '48px',
    height: '48px',
  },
  spinnerCircle: {
    width: '100%',
    height: '100%',
    border: '4px solid #e5e7eb',
    borderTop: '4px solid #3b82f6',
    borderRadius: '50%',
    animation: 'spin 1s linear infinite',
  },
  successIcon: {
    width: '48px',
    height: '48px',
    backgroundColor: '#10b981',
    borderRadius: '50%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    color: '#ffffff',
    fontSize: '24px',
    fontWeight: 'bold',
  },
  errorIcon: {
    width: '48px',
    height: '48px',
    backgroundColor: '#ef4444',
    borderRadius: '50%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    color: '#ffffff',
    fontSize: '24px',
    fontWeight: 'bold',
  },
  title: {
    fontSize: '1.5rem',
    fontWeight: 'bold',
    margin: 0,
  },
  message: {
    fontSize: '0.875rem',
    color: '#6b7280',
    margin: 0,
    textAlign: 'center' as const,
  },
  button: {
    padding: '0.75rem 1.5rem',
    backgroundColor: '#3b82f6',
    color: '#ffffff',
    border: 'none',
    borderRadius: '6px',
    fontSize: '0.875rem',
    fontWeight: '500',
    cursor: 'pointer',
    transition: 'background-color 0.2s ease',
  },
};
