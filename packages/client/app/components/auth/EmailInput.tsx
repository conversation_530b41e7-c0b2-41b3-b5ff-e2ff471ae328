import React, { useState } from 'react';

interface EmailInputProps {
  onSubmit: (email: string) => void;
  placeholder?: string;
  buttonText?: string;
  disabled?: boolean;
}

export function EmailInput({ 
  onSubmit, 
  placeholder = "Enter your email address",
  buttonText = "Continue",
  disabled = false 
}: EmailInputProps) {
  const [email, setEmail] = useState('');
  const [isValid, setIsValid] = useState(false);

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setEmail(value);
    setIsValid(validateEmail(value));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (isValid && !disabled) {
      onSubmit(email);
    }
  };

  return (
    <form onSubmit={handleSubmit} style={styles.form}>
      <div style={styles.inputContainer}>
        <input
          type="email"
          value={email}
          onChange={handleEmailChange}
          placeholder={placeholder}
          style={{
            ...styles.input,
            borderColor: email && !isValid ? '#ef4444' : '#d1d5db',
          }}
          disabled={disabled}
          required
        />
        {email && !isValid && (
          <p style={styles.errorText}>Please enter a valid email address</p>
        )}
      </div>
      <button
        type="submit"
        disabled={!isValid || disabled}
        style={{
          ...styles.button,
          backgroundColor: isValid && !disabled ? '#3b82f6' : '#9ca3af',
          cursor: isValid && !disabled ? 'pointer' : 'not-allowed',
        }}
      >
        {buttonText}
      </button>
    </form>
  );
}

const styles = {
  form: {
    display: 'flex',
    flexDirection: 'column' as const,
    gap: '1rem',
  },
  inputContainer: {
    display: 'flex',
    flexDirection: 'column' as const,
    gap: '0.25rem',
  },
  input: {
    width: '100%',
    padding: '0.75rem',
    border: '1px solid #d1d5db',
    borderRadius: '6px',
    fontSize: '0.875rem',
    outline: 'none',
    transition: 'border-color 0.2s ease',
  },
  errorText: {
    fontSize: '0.75rem',
    color: '#ef4444',
    margin: 0,
  },
  button: {
    width: '100%',
    padding: '0.75rem',
    borderRadius: '6px',
    border: 'none',
    fontSize: '0.875rem',
    fontWeight: '500',
    color: '#ffffff',
    transition: 'background-color 0.2s ease',
  },
};
